quarkus:
  datasource:
    db-kind: h2
    jdbc:
      url: jdbc:h2:mem:test;DB_CLOSE_DELAY=-1;MODE=MySQL
      driver: org.h2.Driver
      transactions: enabled
    username: test
    password: test

  arc:
    exclude-types: com.walnut.vegaspread.common.security.ApiKeyInterceptor
    selected-alternatives: com.walnut.vegaspread.coa.service.TestUserContextService

  keycloak:
    devservices:
      enabled: false

  oidc:
    auth-server-url: http://localhost:8180/realms/myrealm
    client-id: my-client

  hibernate-orm:
    log:
      sql: true
      bind-parameters: true

vega:
  env: test
  cloud:
    provider: gcp
