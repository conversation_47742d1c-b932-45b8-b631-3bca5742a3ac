quarkus:
  jackson:
    fail-on-empty-beans: false
  smallrye-openapi:
    security-scheme: oauth2-implicit

  swagger-ui:
    always-include: true
    oauth-client-id: workflow-swagger-ui

  http:
    root-path: /vegaspread/api/v1/workflow
    limits:
      max-form-attribute-size: 250M
      max-body-size: 250M
    access-log:
      enabled: true
    proxy:
      proxy-address-forwarding: true
      enable-forwarded-host: true
      enable-forwarded-prefix: true
    cors:
      enabled: true
      origins: "*"

  flyway:
    migrate-at-start: true

  hibernate-orm:
    physical-naming-strategy: org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy
    log:
      sql: true
      bind-parameters: true
    envers:
      audit-table-suffix: _aud
      revision-field-name: rev
      revision-type-field-name: revtype
      store-data-at-delete: true

  rest-client:
    logging:
      scope: request-response
    ezee-callback:
      url: ${EZEE_CALLBACK_URL:https://demolend.ezeefin.net.in/centralAPI/v1}

  mailer:
    auth-methods: DIGEST-MD5 CRAM-SHA256 CRAM-SHA1 CRAM-MD5 PLAIN LOGIN
    from: <EMAIL>
    host: smtp.zoho.in
    port: 465
    username: <EMAIL>
    password: ${QUARKUS_MAILER_PASSWORD}
    tls: true

  index-dependency:
    common:
      group-id: com.walnut.vegaspread
      artifact-id: common

org:
  eclipse:
    microprofile:
      rest:
        client:
          propagateHeaders: Authorization,X-API-KEY,X-CLIENT-ID

vega:
  env: ${VEGA_ENV}
  frontend-host-url: https://${vega.env}.vegaspread.cloud
  retry-count:
    rotation: ${RETRY_COUNT:1}
    ocr: ${RETRY_COUNT:1}
    doc-ai: ${RETRY_COUNT:1}
    fs-clf: ${RETRY_COUNT:1}
    processor: ${RETRY_COUNT:1}
    learning: ${RETRY_COUNT:1}
    reprocess: ${RETRY_COUNT:1}
  minutes-before-cleanup: ${MINUTES_BEFORE_CLEANUP:60}


wise:
  client:
    secret: client-secret
