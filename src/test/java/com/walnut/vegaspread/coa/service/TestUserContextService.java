package com.walnut.vegaspread.coa.service;

import com.walnut.vegaspread.common.service.audit.envers.UserContextService;
import io.vertx.ext.web.RoutingContext;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.enterprise.inject.Alternative;
import org.eclipse.microprofile.jwt.JsonWebToken;

@Alternative
@ApplicationScoped
public class TestUserContextService extends UserContextService {

    // Required by Quarkus for proxying
    protected TestUserContextService() {
        super(null, null); // pass nulls since this is only used by the framework
    }

    public TestUserContextService(JsonWebToken jwt,
                                  RoutingContext routingContext) {
        super(jwt, routingContext);
    }

    @Override
    public String getCurrentUsername() {
        return "test"; // fixed username for audit
    }
}
