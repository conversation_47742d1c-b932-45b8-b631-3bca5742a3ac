package com.walnut.vegaspread.coa.service;

import com.walnut.vegaspread.coa.entity.CoaEntity;
import com.walnut.vegaspread.coa.entity.Lvl1CategoryEntity;
import com.walnut.vegaspread.coa.repository.CoaRepository;
import com.walnut.vegaspread.coa.repository.Lvl1CategoryRepository;
import com.walnut.vegaspread.common.model.audit.envers.AuditFilterDto;
import com.walnut.vegaspread.common.model.audit.envers.AuditRequestDto;
import com.walnut.vegaspread.common.model.audit.envers.BaseEntityMapper;
import com.walnut.vegaspread.common.model.audit.envers.MetadataRevEntity;
import com.walnut.vegaspread.common.model.coa.CoaItemDto;
import io.quarkus.test.InjectMock;
import io.quarkus.test.TestTransaction;
import io.quarkus.test.junit.QuarkusTest;
import io.quarkus.test.security.TestSecurity;
import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;
import jakarta.transaction.Transactional;
import org.eclipse.microprofile.jwt.JsonWebToken;
import org.flywaydb.core.Flyway;
import org.hibernate.envers.AuditReader;
import org.hibernate.envers.AuditReaderFactory;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;

import java.time.LocalDateTime;
import java.util.List;

import static com.walnut.vegaspread.common.utils.TestSetup.startMockServer;
import static com.walnut.vegaspread.common.utils.TestSetup.stopMockServer;
import static com.walnut.vegaspread.common.utils.TestSetup.truncateAllTables;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

@QuarkusTest
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class CoaAuditServiceTest {

    private static final String CLIENT_NAME = "walnut";

    @Inject
    Flyway flyway;

    @Inject
    CoaAuditService coaAuditService;

    @InjectMock
    JsonWebToken accessToken;

    @Inject
    CoaRepository coaRepository;

    @Inject
    Lvl1CategoryRepository lvl1CategoryRepository;

    @Inject
    EntityManager entityManager;

    @Inject
    BaseEntityMapper<CoaEntity, CoaItemDto> coaEntityMapper;

    @BeforeAll
    void initDb() {
        flyway.migrate();
        startMockServer();
    }

    @BeforeEach
    void setUp() {
        when(accessToken.getClaim("client_name")).thenReturn(CLIENT_NAME);
    }

    @AfterAll
    void cleanupDb() {
        flyway.clean();
        stopMockServer();
    }

    @BeforeEach
    @Transactional
    void setup() {
        truncateAllTables(entityManager);
        entityManager.createNativeQuery(
                "CREATE SEQUENCE IF NOT EXISTS METADATA_REV_ENTITY_SEQ START WITH 1"
        ).executeUpdate();
    }

    @Transactional
    public Lvl1CategoryEntity createCategory(String name) {
        Lvl1CategoryEntity category = Lvl1CategoryEntity.builder()
                .category(name)
                .createdBy("test")
                .createdTime(LocalDateTime.now())
                .lastModifiedBy("test")
                .lastModifiedTime(LocalDateTime.now())
                .build();
        lvl1CategoryRepository.persist(category);
        return category;
    }

    @Transactional
    public CoaEntity createCoa(String text, String desc, Lvl1CategoryEntity category, Boolean active, Boolean sign) {
        CoaEntity entity = CoaEntity.builder()
                .coaText(text)
                .coaDescription(desc)
                .lvl1Category(category)
                .isActive(active)
                .sign(sign)
                .clientName(CLIENT_NAME)
                .build();
        coaRepository.persist(entity);
        return entity;
    }

    @Transactional
    public void saveCoa(CoaEntity entity) {
        coaRepository.getEntityManager().merge(entity);
    }

    @Test
    void testCreateAndAudit() {
        Lvl1CategoryEntity cat = createCategory("Assets");
        CoaEntity entity = createCoa("1.A.Cash", "Cash Account", cat, true, true);

        // Fetch audit
        var response = coaAuditService.getAuditForCoaId(entity.getCoaId(), coaEntityMapper, entityManager);
        assertNotNull(response);
        assertEquals(1, response.items().size());

        var auditDto = response.items().get(0).dto();
        assertEquals(entity.getCoaText(), auditDto.coaText());
        assertEquals(entity.getCoaDescription(), auditDto.coaDescription());
        assertEquals(entity.getLvl1Category().getId(), auditDto.lvl1CategoryId());
        assertEquals(entity.getLvl1Category().getCategory(), auditDto.lvl1CategoryName());
        assertEquals(entity.isActive, auditDto.isActive());
        assertEquals(entity.getSign(), auditDto.sign());
    }

    @Test
    @TestSecurity(user = "test-user")
    void testUpdateAndAudit() {
        Lvl1CategoryEntity cat = createCategory("Liabilities");
        CoaEntity entity = createCoa("2.L.Debt", "Debt Account", cat, true, false);

        // Update entity
        entity.setCoaDescription("Updated Debt Account");
        saveCoa(entity);

        // Fetch audit
        var response = coaAuditService.getAuditForCoaId(entity.getCoaId(), coaEntityMapper, entityManager);
        assertTrue(response.items().size() >= 2); // Should contain creation + update revisions

        var latestAudit = response.items().get(0).dto();
        assertEquals("Updated Debt Account", latestAudit.coaDescription());
    }

    @Test
    void testDeleteAndAudit() {
        Lvl1CategoryEntity cat = createCategory("Equity");
        CoaEntity entity = createCoa("3.E.Capital", "Capital Account", cat, true, true);

        // Soft delete
        entity.setIsActive(false);
        saveCoa(entity);

        var response = coaAuditService.getAuditForCoaId(entity.getCoaId(), coaEntityMapper, entityManager);
        assertNotNull(response);
        assertTrue(response.items().stream().anyMatch(a -> !a.dto().isActive()));
    }

    @Test
    void testFilterByClientAndAudit() {
        Lvl1CategoryEntity cat = createCategory("Revenue");
        CoaEntity e1 = createCoa("4.R.Sales", "Sales Account", cat, true, true);
        CoaEntity e2 = createCoa("4.R.Service", "Service Account", cat, true, true);

        // Create audit request with filter
        var filter = new AuditFilterDto("clientName", CLIENT_NAME, AuditFilterDto.FilterOperation.EQUALS);
        var request = new AuditRequestDto(List.of(filter), null, 1, 50);
        var response = coaAuditService.getPaginatedAuditsAsDto(request, coaEntityMapper, entityManager);

        assertEquals(2, response.items().size());
        assertTrue(response.items().stream().allMatch(a -> a.dto().clientName().equals(CLIENT_NAME)));
    }

    @Test
    void testSortingAndAudit() {
        Lvl1CategoryEntity cat = createCategory("Expenses");
        CoaEntity e1 = createCoa("5.E.Office", "Office Expenses", cat, true, true);
        CoaEntity e2 = createCoa("5.E.Travel", "Travel Expenses", cat, true, true);

        var request = new AuditRequestDto(null,
                List.of(com.walnut.vegaspread.common.model.audit.envers.AuditSortDto.Builder.byRevisionDateDesc()), 1,
                50);
        var response = coaAuditService.getPaginatedAuditsAsDto(request, coaEntityMapper, entityManager);

        assertTrue(response.items().size() >= 2);
        var first = response.items().get(0).dto();
        var second = response.items().get(1).dto();
        assertNotNull(first.coaId());
        assertNotNull(second.coaId());
    }

    @Test
    @TestTransaction
    void testRollback() {
        // 1️⃣ Create category and initial entity in a separate transaction
        entityManager.persist(createCategory("TestCat"));
        entityManager.flush();
        entityManager.clear();

        Lvl1CategoryEntity category = coaRepository.getEntityManager()
                .find(Lvl1CategoryEntity.class, 1);

        CoaEntity entity = createCoa("Rollback Test", "Rollback Test", category, true, true);
        entityManager.persist(entity);
        entityManager.flush();
        entityManager.clear(); // detach to ensure new revisions are tracked

        // 2️⃣ Update entity in a new "logical transaction"
        entity = coaRepository.findById(entity.getCoaId());
        entity.setCoaDescription("Changed Description");
        entityManager.merge(entity);
        entityManager.flush();
        entityManager.clear(); // detach to finalize revision

        // 3️⃣ Fetch revisions using Envers
        AuditReader reader = AuditReaderFactory.get(entityManager);
        List<Number> revisions = reader.getRevisions(CoaEntity.class, entity.getCoaId());
        assertFalse(revisions.isEmpty(), "No revisions found!"); // safety check

        Number lastRev = revisions.get(revisions.size() - 1);
        MetadataRevEntity revEntity = reader.findRevision(MetadataRevEntity.class, lastRev);
        String traceId = revEntity.getTraceId();

        // 4️⃣ Perform rollback
        coaAuditService.rollback(traceId, entityManager);
        entityManager.flush();
        entityManager.clear();

        // 5️⃣ Validate rollback restored original description
        CoaEntity refreshed = coaRepository.findById(entity.getCoaId());
        assertEquals("Rollback Test", refreshed.getCoaDescription());
    }
}
