package com.walnut.vegaspread.coa.service;

import com.walnut.vegaspread.coa.NoEnversTestProfile;
import com.walnut.vegaspread.coa.entity.WalnutClientCoaMappingEntity;
import com.walnut.vegaspread.coa.model.WalnutClientCoaMappingEntityDto;
import com.walnut.vegaspread.coa.repository.WalnutClientCoaMappingRepository;
import com.walnut.vegaspread.common.exceptions.ResponseException;
import io.quarkus.test.junit.QuarkusTest;
import io.quarkus.test.junit.TestProfile;
import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;
import jakarta.transaction.Transactional;
import jakarta.ws.rs.WebApplicationException;
import org.flywaydb.core.Flyway;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;

import java.util.List;

import static com.walnut.vegaspread.common.utils.TestSetup.startMockServer;
import static com.walnut.vegaspread.common.utils.TestSetup.stopMockServer;
import static com.walnut.vegaspread.common.utils.TestSetup.truncateAllTables;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

@QuarkusTest
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@TestProfile(NoEnversTestProfile.class)
class WalnutClientCoaMappingServiceTest {

    private static final String CLIENT_NAME = "testClient";

    @Inject
    Flyway flyway;
    @Inject
    WalnutClientCoaMappingService walnutClientCoaMappingService;
    @Inject
    WalnutClientCoaMappingRepository repository;
    @Inject
    EntityManager entityManager;

    @BeforeAll
    void setUpDb() {
        flyway.migrate();
        startMockServer();
    }

    @AfterEach
    @Transactional
    void clean() {
        truncateAllTables(entityManager);
    }

    @AfterAll
    void cleanDb() {
        flyway.clean();
        stopMockServer();
    }

    @Test
    void testCreateAndGetMappings() {
        List<WalnutClientCoaMappingEntityDto.CreateOrUpdate> dtos = List.of(
                new WalnutClientCoaMappingEntityDto.CreateOrUpdate(1, 100),
                new WalnutClientCoaMappingEntityDto.CreateOrUpdate(2, 200)
        );

        List<WalnutClientCoaMappingEntity> result = walnutClientCoaMappingService.create(dtos, CLIENT_NAME);

        assertEquals(2, result.size());

        List<WalnutClientCoaMappingEntity> fetched = walnutClientCoaMappingService.get(CLIENT_NAME);
        assertEquals(2, fetched.size());

        assertTrue(fetched.stream().anyMatch(e -> e.getMapping().walnutCoaId.equals(1) &&
                e.getMapping().clientCoaId.equals(100)));
        assertTrue(fetched.stream().anyMatch(e -> e.getMapping().walnutCoaId.equals(2) &&
                e.getMapping().clientCoaId.equals(200)));
    }

    @Test
    void testCreateWithInvalidEntriesThrowsError() {
        List<WalnutClientCoaMappingEntityDto.CreateOrUpdate> dtos = List.of(
                new WalnutClientCoaMappingEntityDto.CreateOrUpdate(null, null),
                new WalnutClientCoaMappingEntityDto.CreateOrUpdate(null, 123),
                new WalnutClientCoaMappingEntityDto.CreateOrUpdate(321, null)
        );

        WebApplicationException ex = assertThrows(WebApplicationException.class, () ->
                walnutClientCoaMappingService.create(dtos, CLIENT_NAME));

        assertEquals(400, ex.getResponse().getStatus());
        ResponseException.ErrorResponse error = (ResponseException.ErrorResponse) ex.getResponse().getEntity();
        assertEquals("Invalid walnutCoaId or clientCoaId in mapping for client: testClient",
                error.message());
    }

    @Test
    void testCreateWithDuplicateWalnutCoaIdThrowsConflict() {
        List<WalnutClientCoaMappingEntityDto.CreateOrUpdate> dtos = List.of(
                new WalnutClientCoaMappingEntityDto.CreateOrUpdate(1, 100),
                new WalnutClientCoaMappingEntityDto.CreateOrUpdate(1, 200)
        );

        WebApplicationException ex = assertThrows(WebApplicationException.class, () ->
                walnutClientCoaMappingService.create(dtos, CLIENT_NAME));

        assertEquals(409, ex.getResponse().getStatus());
        ResponseException.ErrorResponse error = (ResponseException.ErrorResponse) ex.getResponse().getEntity();
        assertEquals("Duplicate walnutCoaId 1 for client: testClient", error.message());
    }

    @Test
    void testCreateWithEmptyListReturnsEmpty() {
        List<WalnutClientCoaMappingEntity> result = walnutClientCoaMappingService.create(List.of(), CLIENT_NAME);
        assertTrue(result.isEmpty());
    }

    @Test
    void testGetWithInvalidClientName() {
        assertTrue(walnutClientCoaMappingService.get(null).isEmpty());
        assertTrue(walnutClientCoaMappingService.get("").isEmpty());
        assertTrue(walnutClientCoaMappingService.get("   ").isEmpty());
    }
}
