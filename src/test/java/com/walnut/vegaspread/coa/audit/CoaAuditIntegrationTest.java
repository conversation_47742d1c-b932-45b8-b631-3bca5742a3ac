package com.walnut.vegaspread.coa.audit;

import com.walnut.vegaspread.coa.entity.CoaEntity;
import com.walnut.vegaspread.coa.entity.Lvl1CategoryEntity;
import com.walnut.vegaspread.coa.repository.CoaRepository;
import com.walnut.vegaspread.coa.repository.Lvl1CategoryRepository;
import com.walnut.vegaspread.coa.service.CoaAuditService;
import com.walnut.vegaspread.common.model.audit.envers.AuditFilterDto;
import com.walnut.vegaspread.common.model.audit.envers.AuditRequestDto;
import com.walnut.vegaspread.common.model.audit.envers.AuditSortDto;
import com.walnut.vegaspread.common.model.audit.envers.MetadataRevEntity;
import com.walnut.vegaspread.common.model.coa.CoaItemDto;
import com.walnut.vegaspread.common.service.audit.envers.GenericAuditService;
import io.quarkus.test.junit.QuarkusTest;
import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;
import jakarta.transaction.Transactional;
import jakarta.transaction.UserTransaction;
import org.flywaydb.core.Flyway;
import org.hibernate.envers.RevisionType;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;

import java.time.LocalDateTime;
import java.util.List;

import static com.walnut.vegaspread.common.utils.TestSetup.startMockServer;
import static com.walnut.vegaspread.common.utils.TestSetup.stopMockServer;
import static com.walnut.vegaspread.common.utils.TestSetup.truncateAllTables;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

@QuarkusTest
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class CoaAuditIntegrationTest {

    private static final String CLIENT_NAME = "walnut";
    private static final String TEST_USER = "testUser";

    @Inject
    CoaAuditService coaAuditService;

    @Inject
    CoaRepository coaRepository;

    @Inject
    Lvl1CategoryRepository lvl1CategoryRepository;

    @Inject
    CoaEntityMapper coaEntityMapper;

    @Inject
    EntityManager entityManager;

    @Inject
    UserTransaction userTransaction;

    @Inject
    Flyway flyway;

    @BeforeAll
    void setUpDb() {
        flyway.migrate();
        startMockServer();
    }

    @AfterEach
    @Transactional
    void clean() {
        truncateAllTables(entityManager);
    }

    @AfterAll
    void cleanDb() {
        flyway.clean();
        stopMockServer();
    }

    @Test
    void testCompleteAuditFlow_CreateUpdateDelete() throws Exception {
        Integer coaId;

        // Step 1: Create entity in separate transaction
        userTransaction.begin();
        try {
            Lvl1CategoryEntity category = createTestCategory("Assets");
            CoaEntity coaEntity = CoaEntity.builder()
                    .coaText("1.BS.CA.Cash")
                    .coaDescription("Cash Account")
                    .clientName(CLIENT_NAME)
                    .isActive(true)
                    .sign(true)
                    .lvl1Category(category)
                    .build();
            entityManager.persist(coaEntity);
            entityManager.flush();
            coaId = coaEntity.getCoaId();
            userTransaction.commit();
        } catch (Exception e) {
            userTransaction.rollback();
            throw e;
        }

        // Step 2: First update in separate transaction
        userTransaction.begin();
        try {
            CoaEntity coaEntity = entityManager.find(CoaEntity.class, coaId);
            coaEntity.setCoaText("1.BS.CA.Cash.Updated");
            coaEntity.setCoaDescription("Updated Cash Account");
            entityManager.flush();
            userTransaction.commit();
        } catch (Exception e) {
            userTransaction.rollback();
            throw e;
        }

        // Step 3: Second update in separate transaction
        userTransaction.begin();
        try {
            CoaEntity coaEntity = entityManager.find(CoaEntity.class, coaId);
            coaEntity.setIsActive(false);
            coaEntity.setSign(false);
            entityManager.flush();
            userTransaction.commit();
        } catch (Exception e) {
            userTransaction.rollback();
            throw e;
        }

        // Step 4: Verify audit trail in separate transaction
        GenericAuditService.PaginatedAuditResponse<GenericAuditService.AuditRevisionDto<CoaItemDto>> response;
        userTransaction.begin();
        try {
            AuditRequestDto request = new AuditRequestDto(
                    List.of(new AuditFilterDto("coaId", coaId, AuditFilterDto.FilterOperation.EQUALS)),
                    List.of(new AuditSortDto("timestamp", AuditSortDto.SortDirection.ASC)),
                    1,
                    10
            );
            response = coaAuditService.getPaginatedAuditsAsDto(request, coaEntityMapper, entityManager);
            userTransaction.commit();
        } catch (Exception e) {
            userTransaction.rollback();
            throw e;
        }

        // Then: Verify complete audit trail
        assertNotNull(response);
        assertTrue(response.items().size() >= 3); // At least 3 revisions (create + 2 updates)

        List<GenericAuditService.AuditRevisionDto<CoaItemDto>> audits = response.items();

        // Verify first revision (creation)
        GenericAuditService.AuditRevisionDto<CoaItemDto> firstRevision = audits.get(0);
        assertEquals(RevisionType.ADD, firstRevision.revisionType());
        assertEquals("1.BS.CA.Cash", firstRevision.dto().coaText());
        assertEquals("Cash Account", firstRevision.dto().coaDescription());
        assertTrue(firstRevision.dto().isActive());
        assertTrue(firstRevision.dto().sign());

        // Verify subsequent revisions show changes
        boolean foundTextUpdate = false;
        boolean foundStatusUpdate = false;

        for (GenericAuditService.AuditRevisionDto<CoaItemDto> audit : audits) {
            if (audit.revisionType() == RevisionType.MOD) {
                if ("1.BS.CA.Cash.Updated".equals(audit.dto().coaText())) {
                    foundTextUpdate = true;
                }
                if (!audit.dto().isActive() && !audit.dto().sign()) {
                    foundStatusUpdate = true;
                }
            }
        }

        assertTrue(foundTextUpdate, "Should find text update in audit trail");
        assertTrue(foundStatusUpdate, "Should find status update in audit trail");
    }

    @Test
    void testAuditWithRelationshipChanges() throws Exception {
        Integer coaId;
        Lvl1CategoryEntity category2;

        // Step 1: Create categories and entity in separate transaction
        userTransaction.begin();
        try {
            Lvl1CategoryEntity category1 = createTestCategory("Assets");
            category2 = createTestCategory("Liabilities");
            CoaEntity coaEntity = createTestCoaEntity("1.BS.CA.Cash", "Cash Account", category1);
            coaId = coaEntity.getCoaId();
            userTransaction.commit();
        } catch (Exception e) {
            userTransaction.rollback();
            throw e;
        }

        // Step 2: Change category relationship in separate transaction
        userTransaction.begin();
        try {
            CoaEntity coaEntity = entityManager.find(CoaEntity.class, coaId);
            coaEntity.setLvl1Category(category2);
            entityManager.flush();
            userTransaction.commit();
        } catch (Exception e) {
            userTransaction.rollback();
            throw e;
        }

        // Step 3: Verify audit captures relationship change in separate transaction
        GenericAuditService.PaginatedAuditResponse<GenericAuditService.AuditRevisionDto<CoaItemDto>> response;
        userTransaction.begin();
        try {
            AuditRequestDto request = new AuditRequestDto(
                    List.of(new AuditFilterDto("coaId", coaId, AuditFilterDto.FilterOperation.EQUALS)),
                    List.of(new AuditSortDto("timestamp", AuditSortDto.SortDirection.DESC)),
                    1,
                    10
            );
            response = coaAuditService.getPaginatedAuditsAsDto(request, coaEntityMapper, entityManager);
            userTransaction.commit();
        } catch (Exception e) {
            userTransaction.rollback();
            throw e;
        }

        assertNotNull(response);
        assertTrue(response.items().size() >= 2); // Creation + update

        // Most recent revision should show new category
        GenericAuditService.AuditRevisionDto<CoaItemDto> latestRevision = response.items().get(0);
        assertEquals(category2.getId(), latestRevision.dto().lvl1CategoryId());
        assertEquals(category2.getCategory(), latestRevision.dto().lvl1CategoryName());
    }

    @Test
    void testAuditWithComplexFiltering() throws Exception {
        Integer cashEntityId;
        Integer bankEntityId;

        // Step 1: Create multiple entities with different characteristics in separate transaction
        userTransaction.begin();
        try {
            Lvl1CategoryEntity assetsCategory = createTestCategory("Assets");
            Lvl1CategoryEntity liabilitiesCategory = createTestCategory("Liabilities");

            CoaEntity cashEntity = createTestCoaEntity("1.BS.CA.Cash", "Cash Account", assetsCategory);
            CoaEntity bankEntity = createTestCoaEntity("1.BS.CA.Bank", "Bank Account", assetsCategory);
            CoaEntity payableEntity = createTestCoaEntity("2.BS.CL.Payable", "Accounts Payable", liabilitiesCategory);

            cashEntityId = cashEntity.getCoaId();
            bankEntityId = bankEntity.getCoaId();
            userTransaction.commit();
        } catch (Exception e) {
            userTransaction.rollback();
            throw e;
        }

        // Step 2: Update some entities in separate transactions
        userTransaction.begin();
        try {
            CoaEntity cashEntity = entityManager.find(CoaEntity.class, cashEntityId);
            cashEntity.setIsActive(false);
            entityManager.flush();
            userTransaction.commit();
        } catch (Exception e) {
            userTransaction.rollback();
            throw e;
        }

        userTransaction.begin();
        try {
            CoaEntity bankEntity = entityManager.find(CoaEntity.class, bankEntityId);
            bankEntity.setCoaDescription("Updated Bank Account");
            entityManager.flush();
            userTransaction.commit();
        } catch (Exception e) {
            userTransaction.rollback();
            throw e;
        }

        // Step 3: Apply complex filters in separate transaction
        GenericAuditService.PaginatedAuditResponse<GenericAuditService.AuditRevisionDto<CoaItemDto>> response;
        userTransaction.begin();
        try {
            List<AuditFilterDto> filters = List.of(
                    new AuditFilterDto("coaText", "1.BS.CA", AuditFilterDto.FilterOperation.LIKE)
            );

            AuditRequestDto request = new AuditRequestDto(
                    filters,
                    List.of(new AuditSortDto("timestamp", AuditSortDto.SortDirection.DESC)),
                    1,
                    10
            );
            response = coaAuditService.getPaginatedAuditsAsDto(request, coaEntityMapper, entityManager);
            userTransaction.commit();
        } catch (Exception e) {
            userTransaction.rollback();
            throw e;
        }

        // Then: Verify filtered results
        assertNotNull(response);
        response.items().forEach(audit -> {
            assertTrue(audit.dto().coaText().contains("1.BS.CA"));
        });
    }

    @Test
    void testAuditPaginationWithLargeDataset() throws Exception {
        // Step 1: Create many entities to test pagination in separate transaction
        userTransaction.begin();
        try {
            Lvl1CategoryEntity category = createTestCategory("Assets");

            for (int i = 1; i <= 20; i++) {
                CoaEntity entity = createTestCoaEntity(
                        "1.BS.CA.Account" + i,
                        "Account " + i,
                        category
                );
                entityManager.flush(); // Flush each creation
            }
            userTransaction.commit();
        } catch (Exception e) {
            userTransaction.rollback();
            throw e;
        }

        // Step 2: Update entities in separate transactions to create more audit records
        for (int i = 1; i <= 20; i++) {
            final int index = i;
            userTransaction.begin();
            try {
                CoaEntity entity = coaRepository.find("coaText", "1.BS.CA.Account" + index).firstResult();
                entity.setCoaDescription("Updated Account " + index);
                entityManager.flush();
                userTransaction.commit();
            } catch (Exception e) {
                userTransaction.rollback();
                throw e;
            }
        }

        // Step 3: Request pages in separate transaction
        GenericAuditService.PaginatedAuditResponse<GenericAuditService.AuditRevisionDto<CoaItemDto>> firstPage;
        GenericAuditService.PaginatedAuditResponse<GenericAuditService.AuditRevisionDto<CoaItemDto>> secondPage;

        userTransaction.begin();
        try {
            // Request first page
            AuditRequestDto firstPageRequest = new AuditRequestDto(
                    List.of(),
                    List.of(new AuditSortDto("timestamp", AuditSortDto.SortDirection.DESC)),
                    1,
                    5
            );
            firstPage = coaAuditService.getPaginatedAuditsAsDto(firstPageRequest, coaEntityMapper, entityManager);

            // Request second page
            AuditRequestDto secondPageRequest = new AuditRequestDto(
                    List.of(),
                    List.of(new AuditSortDto("timestamp", AuditSortDto.SortDirection.DESC)),
                    2,
                    5
            );
            secondPage = coaAuditService.getPaginatedAuditsAsDto(secondPageRequest, coaEntityMapper, entityManager);
            userTransaction.commit();
        } catch (Exception e) {
            userTransaction.rollback();
            throw e;
        }

        // Then: Verify pagination works correctly
        assertNotNull(firstPage);
        assertNotNull(secondPage);

        assertEquals(1, firstPage.pageNumber());
        assertEquals(2, secondPage.pageNumber());

        assertEquals(5, firstPage.items().size());
        assertTrue(secondPage.items().size() <= 5);

        // Verify no overlap between pages
        List<Integer> firstPageRevisions = firstPage.items().stream()
                .map(item -> ((MetadataRevEntity) item.revisionEntity()).getId())
                .toList();
        List<Integer> secondPageRevisions = secondPage.items().stream()
                .map(item -> ((MetadataRevEntity) item.revisionEntity()).getId())
                .toList();

        assertTrue(firstPageRevisions.stream().noneMatch(secondPageRevisions::contains));
    }

    @Test
    void testAuditWithModifiedFlags() throws Exception {
        Integer coaId;

        // Step 1: Create entity in separate transaction
        userTransaction.begin();
        try {
            Lvl1CategoryEntity category = createTestCategory("Assets");
            CoaEntity coaEntity = createTestCoaEntity("1.BS.CA.Cash", "Cash Account", category);
            coaId = coaEntity.getCoaId();
            userTransaction.commit();
        } catch (Exception e) {
            userTransaction.rollback();
            throw e;
        }

        // Step 2: Update specific fields in separate transaction
        userTransaction.begin();
        try {
            CoaEntity coaEntity = entityManager.find(CoaEntity.class, coaId);
            coaEntity.setCoaText("1.BS.CA.Cash.Updated");
            // Don't change coaDescription, isActive, sign, or lvl1Category
            entityManager.flush();
            userTransaction.commit();
        } catch (Exception e) {
            userTransaction.rollback();
            throw e;
        }

        // Step 3: Verify audit captures the change in separate transaction
        GenericAuditService.PaginatedAuditResponse<GenericAuditService.AuditRevisionDto<CoaItemDto>> response;
        userTransaction.begin();
        try {
            AuditRequestDto request = new AuditRequestDto(
                    List.of(new AuditFilterDto("coaId", coaId, AuditFilterDto.FilterOperation.EQUALS)),
                    List.of(new AuditSortDto("timestamp", AuditSortDto.SortDirection.DESC)),
                    1,
                    10
            );
            response = coaAuditService.getPaginatedAuditsAsDto(request, coaEntityMapper, entityManager);
            userTransaction.commit();
        } catch (Exception e) {
            userTransaction.rollback();
            throw e;
        }

        assertNotNull(response);
        assertTrue(response.items().size() >= 2); // Creation + update

        // Latest revision should show the updated text
        GenericAuditService.AuditRevisionDto<CoaItemDto> latestRevision = response.items().get(0);
        assertEquals(RevisionType.MOD, latestRevision.revisionType());
        assertEquals("1.BS.CA.Cash.Updated", latestRevision.dto().coaText());
    }

    @Test
    void testAuditWithBooleanValueChanges() throws Exception {
        Integer coaId;

        // Step 1: Create entity with initial boolean values in separate transaction
        userTransaction.begin();
        try {
            Lvl1CategoryEntity category = createTestCategory("Assets");
            CoaEntity coaEntity = CoaEntity.builder()
                    .coaText("1.BS.CA.Cash")
                    .coaDescription("Cash Account")
                    .clientName(CLIENT_NAME)
                    .isActive(false) // Initial value
                    .sign(true) // Initial value
                    .lvl1Category(category)
                    .build();
            coaRepository.persist(coaEntity);
            entityManager.flush();
            coaId = coaEntity.getCoaId();
            userTransaction.commit();
        } catch (Exception e) {
            userTransaction.rollback();
            throw e;
        }

        // Step 2: Update boolean values in separate transaction
        userTransaction.begin();
        try {
            CoaEntity coaEntity = entityManager.find(CoaEntity.class, coaId);
            coaEntity.setIsActive(true);
            coaEntity.setSign(false);
            entityManager.flush();
            userTransaction.commit();
        } catch (Exception e) {
            userTransaction.rollback();
            throw e;
        }

        // Step 3: Verify audit captures boolean value changes in separate transaction
        GenericAuditService.PaginatedAuditResponse<GenericAuditService.AuditRevisionDto<CoaItemDto>> response;
        userTransaction.begin();
        try {
            AuditRequestDto request = new AuditRequestDto(
                    List.of(new AuditFilterDto("coaId", coaId, AuditFilterDto.FilterOperation.EQUALS)),
                    List.of(new AuditSortDto("timestamp", AuditSortDto.SortDirection.ASC)),
                    1,
                    10
            );
            response = coaAuditService.getPaginatedAuditsAsDto(request, coaEntityMapper, entityManager);
            userTransaction.commit();
        } catch (Exception e) {
            userTransaction.rollback();
            throw e;
        }

        assertNotNull(response);
        assertTrue(response.items().size() >= 2);

        // First revision should have initial values
        GenericAuditService.AuditRevisionDto<CoaItemDto> firstRevision = response.items().get(0);
        assertFalse(firstRevision.dto().isActive());
        assertTrue(firstRevision.dto().sign());

        // Second revision should have updated values
        GenericAuditService.AuditRevisionDto<CoaItemDto> secondRevision = response.items().get(1);
        assertTrue(secondRevision.dto().isActive());
        assertFalse(secondRevision.dto().sign());
    }

    // Helper methods
    @Transactional
    public Lvl1CategoryEntity createTestCategory(String categoryName) {
        Lvl1CategoryEntity category = Lvl1CategoryEntity.builder()
                .category(categoryName)
                .createdBy(TEST_USER)
                .createdTime(LocalDateTime.now())
                .lastModifiedBy(TEST_USER)
                .lastModifiedTime(LocalDateTime.now())
                .build();
        lvl1CategoryRepository.persist(category);
        return category;
    }

    @Transactional
    public CoaEntity createTestCoaEntity(String coaText, String description, Lvl1CategoryEntity category) {
        CoaEntity entity = CoaEntity.builder()
                .coaText(coaText)
                .coaDescription(description)
                .clientName(CLIENT_NAME)
                .isActive(true)
                .sign(true)
                .lvl1Category(category)
                .build();
        coaRepository.persist(entity);
        return entity;
    }
}
