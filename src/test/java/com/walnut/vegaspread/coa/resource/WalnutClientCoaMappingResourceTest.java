package com.walnut.vegaspread.coa.resource;

import com.walnut.vegaspread.coa.NoEnversTestProfile;
import com.walnut.vegaspread.coa.entity.WalnutClientCoaMappingEntity;
import com.walnut.vegaspread.coa.model.WalnutClientCoaMappingEntityDto;
import com.walnut.vegaspread.coa.primarykey.WalnutClientCoaMappingPk;
import com.walnut.vegaspread.coa.repository.WalnutClientCoaMappingRepository;
import com.walnut.vegaspread.common.roles.Roles;
import io.quarkus.test.common.http.TestHTTPEndpoint;
import io.quarkus.test.junit.QuarkusTest;
import io.quarkus.test.junit.TestProfile;
import io.quarkus.test.security.TestSecurity;
import io.restassured.common.mapper.TypeRef;
import io.restassured.http.ContentType;
import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;
import jakarta.transaction.Transactional;
import org.flywaydb.core.Flyway;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static com.walnut.vegaspread.coa.resource.CommonTestUtils.CLIENT_NAME;
import static com.walnut.vegaspread.coa.resource.CommonTestUtils.NAME;
import static com.walnut.vegaspread.common.utils.TestSetup.startMockServer;
import static com.walnut.vegaspread.common.utils.TestSetup.stopMockServer;
import static com.walnut.vegaspread.common.utils.TestSetup.truncateAllTables;
import static io.restassured.RestAssured.given;

@QuarkusTest
@TestHTTPEndpoint(WalnutClientCoaMappingResource.class)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@TestProfile(NoEnversTestProfile.class)
class WalnutClientCoaMappingResourceTest {

    @Inject
    Flyway flyway;

    @Inject
    WalnutClientCoaMappingRepository walnutClientCoaMappingRepository;

    @Inject
    EntityManager entityManager;

    @BeforeAll
    void setUpDb() {
        flyway.migrate();
        startMockServer();
    }

    @AfterEach
    @Transactional
    void clean() {
        truncateAllTables(entityManager);
    }

    @AfterAll
    void cleanDb() {
        flyway.clean();
        stopMockServer();
    }

    @Transactional
    List<WalnutClientCoaMappingEntity> addWalnutClientCoaMapping() {
        List<WalnutClientCoaMappingEntity> walnutClientCoaMappingEntities = new ArrayList<>();
        WalnutClientCoaMappingEntity walnutClientCoaMappingEntity1 = new WalnutClientCoaMappingEntity(
                new WalnutClientCoaMappingPk(1, 1, CLIENT_NAME));
        walnutClientCoaMappingEntities.add(walnutClientCoaMappingEntity1);
        WalnutClientCoaMappingEntity walnutClientCoaMappingEntity2 = new WalnutClientCoaMappingEntity(
                new WalnutClientCoaMappingPk(2, 1, CLIENT_NAME));
        walnutClientCoaMappingEntities.add(walnutClientCoaMappingEntity2);
        WalnutClientCoaMappingEntity walnutClientCoaMappingEntity3 = new WalnutClientCoaMappingEntity(
                new WalnutClientCoaMappingPk(3, 1, CLIENT_NAME));
        walnutClientCoaMappingEntities.add(walnutClientCoaMappingEntity3);
        WalnutClientCoaMappingEntity walnutClientCoaMappingEntity4 = new WalnutClientCoaMappingEntity(
                new WalnutClientCoaMappingPk(1, 4, "Client Name 2"));
        walnutClientCoaMappingEntities.add(walnutClientCoaMappingEntity4);

        walnutClientCoaMappingRepository.persist(walnutClientCoaMappingEntities);
        return walnutClientCoaMappingEntities;
    }

    @Test
    @TestSecurity(user = NAME, roles = Roles.SUPERADMIN)
    void testCreateForNullEntityList() {
        List<WalnutClientCoaMappingEntity> response = given()
                .contentType(ContentType.JSON).body("")
                .queryParam("clientName", CLIENT_NAME)
                .when().post("")
                .then().statusCode(200)
                .extract().body().as(new TypeRef<>() {
                });
        Assertions.assertTrue(response.isEmpty());
    }

    @Test
    @TestSecurity(user = NAME, roles = Roles.SUPERADMIN)
    void testCreateForEmptyEntityList() {
        List<WalnutClientCoaMappingEntity> response = given()
                .contentType(ContentType.JSON).body(Collections.emptyList())
                .queryParam("clientName", CLIENT_NAME)
                .when().post("")
                .then().statusCode(200).extract().body().as(new TypeRef<>() {
                });
        Assertions.assertTrue(response.isEmpty());
    }

    @Test
    @TestSecurity(user = NAME, roles = Roles.SUPERADMIN)
    void testCreate() {
        addWalnutClientCoaMapping();
        List<WalnutClientCoaMappingEntityDto.CreateOrUpdate> walnutClientCoaMappingDtos = new ArrayList<>();

        WalnutClientCoaMappingEntityDto.CreateOrUpdate mapping1 = new WalnutClientCoaMappingEntityDto.CreateOrUpdate(1,
                2);
        walnutClientCoaMappingDtos.add(mapping1);
        WalnutClientCoaMappingEntityDto.CreateOrUpdate mapping2 = new WalnutClientCoaMappingEntityDto.CreateOrUpdate(2,
                2);
        walnutClientCoaMappingDtos.add(mapping2);
        WalnutClientCoaMappingEntityDto.CreateOrUpdate mapping3 = new WalnutClientCoaMappingEntityDto.CreateOrUpdate(4,
                3);
        walnutClientCoaMappingDtos.add(mapping3);
        WalnutClientCoaMappingEntityDto.CreateOrUpdate mapping4 = new WalnutClientCoaMappingEntityDto.CreateOrUpdate(
                null, null);
        walnutClientCoaMappingDtos.add(mapping4);

        given()
                .contentType(ContentType.JSON).body(walnutClientCoaMappingDtos)
                .queryParam("clientName", CLIENT_NAME)
                .when().post("")
                .then().statusCode(400);
    }

    @Test
    @TestSecurity(user = NAME, roles = Roles.SUPERADMIN)
    void testGet() {
        List<WalnutClientCoaMappingEntity> walnutClientCoaMappingEntities = addWalnutClientCoaMapping();

        List<WalnutClientCoaMappingEntity> walnutClientCoaMappingEntitiesResponse = given()
                .when().get("/{clientName}", CLIENT_NAME)
                .then().statusCode(200).extract().body().as(new TypeRef<>() {
                });
        List<WalnutClientCoaMappingEntity> expectedWalnutCoaMappingEntitites = walnutClientCoaMappingEntities.stream()
                .filter(walnutClientCoaMappingEntity -> walnutClientCoaMappingEntity.getMapping().clientName
                        .equals(CLIENT_NAME))
                .toList();
        for (int i = 0; i < expectedWalnutCoaMappingEntitites.size(); i++) {
            Assertions.assertEquals(expectedWalnutCoaMappingEntitites.get(i).getMapping().clientName,
                    walnutClientCoaMappingEntitiesResponse.get(i).getMapping().clientName);
            Assertions.assertEquals(expectedWalnutCoaMappingEntitites.get(i).getMapping().clientCoaId,
                    walnutClientCoaMappingEntitiesResponse.get(i).getMapping().clientCoaId);
            Assertions.assertEquals(expectedWalnutCoaMappingEntitites.get(i).getMapping().walnutCoaId,
                    walnutClientCoaMappingEntitiesResponse.get(i).getMapping().walnutCoaId);
        }
    }
}
