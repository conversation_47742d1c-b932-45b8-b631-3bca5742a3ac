package com.walnut.vegaspread.coa.resource;

import com.walnut.vegaspread.coa.entity.CoaEntity;
import com.walnut.vegaspread.coa.entity.Lvl1CategoryEntity;
import com.walnut.vegaspread.coa.entity.WalnutClientCoaMappingEntity;
import com.walnut.vegaspread.coa.model.WalnutClientCoaMappingEntityDto;
import com.walnut.vegaspread.coa.primarykey.WalnutClientCoaMappingPk;
import com.walnut.vegaspread.coa.repository.CoaRepository;
import com.walnut.vegaspread.coa.repository.Lvl1CategoryRepository;
import com.walnut.vegaspread.coa.repository.WalnutClientCoaMappingRepository;
import com.walnut.vegaspread.coa.service.ExchangeService;
import com.walnut.vegaspread.common.model.coa.CoaItemDto;
import com.walnut.vegaspread.common.roles.Roles;
import io.quarkus.test.InjectMock;
import io.quarkus.test.common.http.TestHTTPEndpoint;
import io.quarkus.test.junit.QuarkusTest;
import io.quarkus.test.security.TestSecurity;
import io.restassured.common.mapper.TypeRef;
import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;
import jakarta.transaction.Transactional;
import org.flywaydb.core.Flyway;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.IntStream;

import static com.walnut.vegaspread.coa.resource.CommonTestUtils.CLIENT_NAME;
import static com.walnut.vegaspread.coa.resource.CommonTestUtils.NAME;
import static com.walnut.vegaspread.common.utils.TestSetup.truncateAllTables;
import static io.restassured.RestAssured.given;

@QuarkusTest
@TestHTTPEndpoint(ProcessorApiResource.class)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class ProcessorApiResourceTest {

    @InjectMock
    ExchangeService exchangeService;

    @Inject
    Flyway flyway;

    @Inject
    CoaRepository coaRepository;

    @Inject
    WalnutClientCoaMappingRepository walnutClientCoaMappingRepository;

    @Inject
    Lvl1CategoryRepository lvl1CategoryRepository;

    @Inject
    EntityManager entityManager;

    @BeforeAll
    void setUpDb() {
        flyway.migrate();
    }

    @AfterEach
    @Transactional
    void clean() {
        truncateAllTables(entityManager);
    }

    @AfterAll
    void cleanDb() {
        flyway.clean();
    }

    @Transactional
    List<WalnutClientCoaMappingEntity> addWalnutClientCoaMapping() {
        List<WalnutClientCoaMappingEntity> walnutClientCoaMappingEntities = new ArrayList<>();
        WalnutClientCoaMappingEntity walnutClientCoaMappingEntity1 = new WalnutClientCoaMappingEntity(
                new WalnutClientCoaMappingPk(1, 1, CLIENT_NAME));
        walnutClientCoaMappingEntities.add(walnutClientCoaMappingEntity1);
        WalnutClientCoaMappingEntity walnutClientCoaMappingEntity2 = new WalnutClientCoaMappingEntity(
                new WalnutClientCoaMappingPk(2, 1, CLIENT_NAME));
        walnutClientCoaMappingEntities.add(walnutClientCoaMappingEntity2);
        WalnutClientCoaMappingEntity walnutClientCoaMappingEntity3 = new WalnutClientCoaMappingEntity(
                new WalnutClientCoaMappingPk(3, 1, CLIENT_NAME));
        walnutClientCoaMappingEntities.add(walnutClientCoaMappingEntity3);
        WalnutClientCoaMappingEntity walnutClientCoaMappingEntity4 = new WalnutClientCoaMappingEntity(
                new WalnutClientCoaMappingPk(1, 4, "Client Name 2"));
        walnutClientCoaMappingEntities.add(walnutClientCoaMappingEntity4);

        walnutClientCoaMappingRepository.persist(walnutClientCoaMappingEntities);
        return walnutClientCoaMappingEntities;
    }

    @Transactional
    List<Lvl1CategoryEntity> addNewCategory(List<String> categories) {
        List<Lvl1CategoryEntity> lvl1CategoryEntities = categories.stream().map(category -> Lvl1CategoryEntity.builder()
                .category(category)
                .createdBy("user")
                .createdTime(LocalDateTime.now())
                .lastModifiedBy("user")
                .lastModifiedTime(LocalDateTime.now())
                .build()).toList();
        lvl1CategoryRepository.persist(lvl1CategoryEntities);
        return lvl1CategoryEntities;
    }

    @Transactional
    List<CoaEntity> addCoaEntities() {
        List<Lvl1CategoryEntity> lvl1CategoryEntities = addNewCategory(
                IntStream.range(1, 11).mapToObj(id -> "category" + id).toList());
        List<CoaEntity> coaEntities = IntStream.range(1, 11).mapToObj(id -> CoaEntity.builder()
                .coaText("coaText" + id)
                .clientName(CLIENT_NAME)
                .coaDescription("coaDesc" + id)
                .lvl1Category(lvl1CategoryEntities.get(id - 1))
                .isActive(id % 2 == 0)
                .sign(id % 2 == 0)
                .build()).toList();

        coaRepository.persist(coaEntities);
        return coaEntities;
    }

    @Test
    @TestSecurity(user = NAME)
    void testListActive() {
        List<CoaEntity> coaEntities = addCoaEntities();

        List<CoaEntity> activeCoaEntites = coaEntities.stream().filter(CoaEntity::getIsActive).toList();

        List<CoaItemDto> entityList = given()
                .queryParam("clientName", CLIENT_NAME)
                .when().get("/list")
                .then().statusCode(200)
                .extract().body().as(new TypeRef<>() {
                });

        for (int i = 0; i < entityList.size(); i++) {
            Assertions.assertEquals(activeCoaEntites.get(i).getCoaId(), entityList.get(i).coaId());
            Assertions.assertEquals(activeCoaEntites.get(i).getCoaText(), entityList.get(i).coaText());
            Assertions.assertEquals(activeCoaEntites.get(i).getCoaDescription(), entityList.get(i).coaDescription());
            Assertions.assertEquals(activeCoaEntites.get(i).getClientName(), entityList.get(i).clientName());
            Assertions.assertEquals(activeCoaEntites.get(i).getLvl1Category().getCategory(),
                    entityList.get(i).lvl1Category());
            Assertions.assertEquals(activeCoaEntites.get(i).getIsActive(), entityList.get(i).isActive());
        }
    }

    @Test
    @TestSecurity(user = NAME, roles = Roles.SUPERADMIN)
    void testGet() {
        List<WalnutClientCoaMappingEntity> walnutClientCoaMappingEntities = addWalnutClientCoaMapping();

        List<WalnutClientCoaMappingEntityDto.CreateOrUpdate> responseDtos = given()
                .queryParam("clientName", CLIENT_NAME)
                .when().get("/walnut-client-mapping")
                .then().statusCode(200).extract().body().as(new TypeRef<>() {
                });
        List<WalnutClientCoaMappingEntity> expectedWalnutCoaMappingEntitites = walnutClientCoaMappingEntities.stream()
                .filter(walnutClientCoaMappingEntity -> walnutClientCoaMappingEntity.getMapping().clientName.equals(
                        CLIENT_NAME))
                .toList();
        for (int i = 0; i < expectedWalnutCoaMappingEntitites.size(); i++) {
            Assertions.assertEquals(expectedWalnutCoaMappingEntitites.get(i).getMapping().clientCoaId,
                    responseDtos.get(i).clientCoaId());
            Assertions.assertEquals(expectedWalnutCoaMappingEntitites.get(i).getMapping().walnutCoaId,
                    responseDtos.get(i).walnutCoaId());
        }
    }
}
