package com.walnut.vegaspread.coa.resource;

import com.walnut.vegaspread.coa.NoEnversTestProfile;
import io.quarkus.test.common.http.TestHTTPEndpoint;
import io.quarkus.test.junit.QuarkusTest;
import io.quarkus.test.junit.TestProfile;

//TODO Might not be used in the future.
@QuarkusTest
@TestHTTPEndpoint(CoaTaskResource.class)
@TestProfile(NoEnversTestProfile.class)
class CoaTaskResourceTest {

   /* @Inject
    CoaRepository coaRepository;

    @Inject
    CoaTaskRepository coaTaskRepository;

    @Inject
    Flyway flyway;

    @BeforeEach
    void setUp() {
        flyway.migrate();
    }

    @AfterEach
    void clean() {
        flyway.clean();
    }

    @Transactional
    List<CoaEntity> createCoas() {
        List<CoaEntity> coaEntities = IntStream.range(1, 11).mapToObj(id -> CoaEntity.builder()
                .coaText("Coa text " + id)
                .clientName(id % 2 == 0 ? "ClientName1" : "ClientName2")
                .coaDescription("Coa Description " + id)
                .lvl1Category(id % 2 == 0 ? "Category 1" : "Category 2")
                .isActive(id % 2 == 0)
                .build()).toList();

        coaRepository.persist(coaEntities);
        return coaEntities;
    }

    @Transactional
    CoaTaskEntity addNewTask(CoaTaskDto.NewTask task) {
        CoaTaskEntity coaTaskEntity = CoaTaskEntity.builder()
                .spreadId(task.spreadId())
                .blockId(task.blockId())
                .rowId(task.rowId().byteValue())
                .tableTypeId(task.tableTypeId())
                .rowParent(task.rowParent())
                .text(task.text())
                .fsHeader(task.fsHeader())
                .fsText(task.fsText())
                .clientName(CLIENT_NAME)
                .coa(coaRepository.findById(task.coaId()))
                .status(StatusEnum.TO_REVIEW)
                .createdBy(USERNAME)
                .createdByFullName(NAME)
                .createdTime(LocalDateTime.of(2024, 6, 17, 1, 1, 1, 1))
                .reviewedBy(USERNAME)
                .reviewedTime(LocalDateTime.of(2024, 6, 19, 1, 1, 1, 1))
                .build();
        coaTaskRepository.persist(coaTaskEntity);
        return coaTaskEntity;
    }

    @Test
    @TestSecurity(roles = {Roles.MAP_COA})
    void testCreate() {
        createCoas();
        CoaTaskDto.NewTask newTask = new CoaTaskDto.NewTask(1, 1, 1, 1, "BS",
                "rowParent", "text", "fsHeader", "fsText", 1);

        CoaTaskEntity task = given()
                .contentType(ContentType.JSON)
                .body(newTask)
                .when().post("/task")
                .then()
                .statusCode(200)
                .extract().body().as(CoaTaskEntity.class);

        Assertions.assertEquals(1, task.id);
        Assertions.assertEquals(newTask.spreadId(), task.spreadId);
        Assertions.assertEquals(newTask.blockId(), task.blockId);
        Assertions.assertEquals(newTask.rowId().byteValue(), task.rowId);
        Assertions.assertEquals(newTask.tableTypeId(), task.tableTypeId);
        Assertions.assertEquals(newTask.rowParent(), task.rowParent);
        Assertions.assertEquals(newTask.text(), task.text);
        Assertions.assertEquals(newTask.fsText(), task.fsText);
        Assertions.assertEquals(newTask.fsHeader(), task.fsHeader);
        Assertions.assertEquals(CLIENT_NAME, task.clientName);
        Assertions.assertEquals(newTask.coaId(), task.coa.coaId);
        Assertions.assertEquals(StatusEnum.TO_REVIEW, task.status);
        Assertions.assertEquals(USERNAME, task.createdBy);
    }

    @Test
    void testUpdate() {
        addCoaList();
        CoaTaskEntity coaTaskEntity = addNewTask(newTask);
        Mockito.when(iamClient.getNameFromUsername(USERNAME)).thenReturn(NAME);

        CoaTaskDto.UpdateTask updateTask = new CoaTaskDto.UpdateTask(1, 2, StatusEnum.APPROVED);

        TaskOutputDto updatedTask = given().auth().oauth2(accessToken)
                .contentType(ContentType.JSON).body(updateTask)
                .when().patch("/task")
                .then().statusCode(200)
                .extract().body().as(TaskOutputDto.class);

        TaskOutputDto taskOutputDto = new TaskOutputDto(1, coaTaskEntity.spreadId, coaTaskEntity.blockId,
                coaTaskEntity.rowId,
                coaTaskEntity.tableTypeId, updateTask.coaId(), updateTask.status(), coaTaskEntity.createdBy,
                coaTaskEntity.createdByFullName,
                coaTaskEntity.createdTime, USERNAME, NAME, LocalDateTime.now());

        Assertions.assertEquals(taskOutputDto.taskId(), updatedTask.taskId());
        Assertions.assertEquals(taskOutputDto.spreadId(), updatedTask.spreadId());
        Assertions.assertEquals(taskOutputDto.blockId(), updatedTask.blockId());
        Assertions.assertEquals(taskOutputDto.rowId(), updatedTask.rowId());
        Assertions.assertEquals(taskOutputDto.tableTypeId(), updatedTask.tableTypeId());
        Assertions.assertEquals(taskOutputDto.coaId(), updatedTask.coaId());
        Assertions.assertEquals(taskOutputDto.status(), updatedTask.status());
        Assertions.assertEquals(taskOutputDto.createdBy(), updatedTask.createdBy());
        Assertions.assertEquals(taskOutputDto.createdByFullName(), updatedTask.createdByFullName());
        Assertions.assertEquals(taskOutputDto.reviewedBy(), updatedTask.reviewedBy());
        Assertions.assertEquals(taskOutputDto.reviewedByFullName(), updatedTask.reviewedByFullName());
    }

    @Test
    void testUpdateWithStatusNull() {
        addCoaList();
        CoaTaskEntity coaTaskEntity = addNewTask(newTask);
        Mockito.when(iamClient.getNameFromUsername(USERNAME)).thenReturn(NAME);

        CoaTaskDto.UpdateTask updateTask = new CoaTaskDto.UpdateTask(1, 2, null);

        TaskOutputDto updatedTask = given().auth().oauth2(accessToken)
                .contentType(ContentType.JSON).body(updateTask)
                .when().patch("/task")
                .then().statusCode(200)
                .extract().body().as(TaskOutputDto.class);

        TaskOutputDto taskOutputDto = new TaskOutputDto(1, coaTaskEntity.spreadId, coaTaskEntity.blockId,
                coaTaskEntity.rowId,
                coaTaskEntity.tableTypeId, coaTaskEntity.coa.coaId, updatedTask.status(), coaTaskEntity.createdBy,
                coaTaskEntity.createdByFullName,
                coaTaskEntity.createdTime, coaTaskEntity.reviewedBy, coaTaskEntity.reviewedByFullName,
                coaTaskEntity.reviewedTime);

        Assertions.assertEquals(taskOutputDto.taskId(), updatedTask.taskId());
        Assertions.assertEquals(taskOutputDto.spreadId(), updatedTask.spreadId());
        Assertions.assertEquals(taskOutputDto.blockId(), updatedTask.blockId());
        Assertions.assertEquals(taskOutputDto.rowId(), updatedTask.rowId());
        Assertions.assertEquals(taskOutputDto.tableTypeId(), updatedTask.tableTypeId());
        Assertions.assertEquals(taskOutputDto.coaId(), updatedTask.coaId());
        Assertions.assertEquals(taskOutputDto.status(), updatedTask.status());
        Assertions.assertEquals(taskOutputDto.createdBy(), updatedTask.createdBy());
        Assertions.assertEquals(taskOutputDto.createdByFullName(), updatedTask.createdByFullName());
        Assertions.assertEquals(taskOutputDto.reviewedBy(), updatedTask.reviewedBy());
        Assertions.assertEquals(taskOutputDto.reviewedByFullName(), updatedTask.reviewedByFullName());
    }

    @Test
    void testUpdateWithStatusToReview() {
        addCoaList();
        CoaTaskEntity coaTaskEntity = addNewTask(newTask);
        Mockito.when(iamClient.getNameFromUsername(USERNAME)).thenReturn(NAME);

        CoaTaskDto.UpdateTask updateTask = new CoaTaskDto.UpdateTask(1, 2, StatusEnum.TO_REVIEW);

        TaskOutputDto updatedTask = given().auth().oauth2(accessToken)
                .contentType(ContentType.JSON).body(updateTask)
                .when().patch("/task")
                .then().statusCode(200)
                .extract().body().as(TaskOutputDto.class);

        TaskOutputDto taskOutputDto = new TaskOutputDto(1, coaTaskEntity.spreadId, coaTaskEntity.blockId,
                coaTaskEntity.rowId,
                coaTaskEntity.tableTypeId, coaTaskEntity.coa.coaId, coaTaskEntity.status, coaTaskEntity.createdBy,
                coaTaskEntity.createdByFullName,
                coaTaskEntity.createdTime, coaTaskEntity.reviewedBy, coaTaskEntity.reviewedByFullName,
                coaTaskEntity.reviewedTime);

        Assertions.assertEquals(taskOutputDto.taskId(), updatedTask.taskId());
        Assertions.assertEquals(taskOutputDto.spreadId(), updatedTask.spreadId());
        Assertions.assertEquals(taskOutputDto.blockId(), updatedTask.blockId());
        Assertions.assertEquals(taskOutputDto.rowId(), updatedTask.rowId());
        Assertions.assertEquals(taskOutputDto.tableTypeId(), updatedTask.tableTypeId());
        Assertions.assertEquals(taskOutputDto.coaId(), updatedTask.coaId());
        Assertions.assertEquals(taskOutputDto.status(), updatedTask.status());
        Assertions.assertEquals(taskOutputDto.createdBy(), updatedTask.createdBy());
        Assertions.assertEquals(taskOutputDto.createdByFullName(), updatedTask.createdByFullName());
        Assertions.assertEquals(taskOutputDto.reviewedBy(), updatedTask.reviewedBy());
        Assertions.assertEquals(taskOutputDto.reviewedByFullName(), updatedTask.reviewedByFullName());
    }

    @Test
    void testUpdateWithCoaIdNull() {
        addCoaList();
        CoaTaskEntity coaTaskEntity = addNewTask(newTask);
        Mockito.when(iamClient.getNameFromUsername(USERNAME)).thenReturn(NAME);
        CoaTaskDto.UpdateTask updateTask = new CoaTaskDto.UpdateTask(1, null, StatusEnum.APPROVED);

        TaskOutputDto updatedTask = given().auth().oauth2(accessToken)
                .contentType(ContentType.JSON).body(updateTask)
                .when().patch("/task")
                .then().statusCode(200)
                .extract().body().as(TaskOutputDto.class);

        TaskOutputDto taskOutputDto = new TaskOutputDto(1, coaTaskEntity.spreadId, coaTaskEntity.blockId,
                coaTaskEntity.rowId,
                coaTaskEntity.tableTypeId, coaTaskEntity.coa.coaId, updatedTask.status(), coaTaskEntity.createdBy,
                coaTaskEntity.createdByFullName,
                coaTaskEntity.createdTime, USERNAME, NAME, coaTaskEntity.reviewedTime);

        Assertions.assertEquals(taskOutputDto.taskId(), updatedTask.taskId());
        Assertions.assertEquals(taskOutputDto.spreadId(), updatedTask.spreadId());
        Assertions.assertEquals(taskOutputDto.blockId(), updatedTask.blockId());
        Assertions.assertEquals(taskOutputDto.rowId(), updatedTask.rowId());
        Assertions.assertEquals(taskOutputDto.tableTypeId(), updatedTask.tableTypeId());
        Assertions.assertEquals(taskOutputDto.coaId(), updatedTask.coaId());
        Assertions.assertEquals(taskOutputDto.status(), updatedTask.status());
        Assertions.assertEquals(taskOutputDto.createdBy(), updatedTask.createdBy());
        Assertions.assertEquals(taskOutputDto.createdByFullName(), updatedTask.createdByFullName());
        Assertions.assertEquals(taskOutputDto.reviewedBy(), updatedTask.reviewedBy());
        Assertions.assertEquals(taskOutputDto.reviewedByFullName(), updatedTask.reviewedByFullName());
    }

    @Test
    void testUpdateWithNewCoaIdSameAsExisting() {
        addCoaList();
        CoaTaskEntity coaTaskEntity = addNewTask(newTask);
        Mockito.when(iamClient.getNameFromUsername(USERNAME)).thenReturn(NAME);

        CoaTaskDto.UpdateTask updateTask = new CoaTaskDto.UpdateTask(1, 1, StatusEnum.APPROVED);

        TaskOutputDto updatedTask = given().auth().oauth2(accessToken)
                .contentType(ContentType.JSON).body(updateTask)
                .when().patch("/task")
                .then().statusCode(200)
                .extract().body().as(TaskOutputDto.class);

        TaskOutputDto taskOutputDto = new TaskOutputDto(1, coaTaskEntity.spreadId, coaTaskEntity.blockId,
                coaTaskEntity.rowId,
                coaTaskEntity.tableTypeId, coaTaskEntity.coa.coaId, updatedTask.status(), coaTaskEntity.createdBy,
                coaTaskEntity.createdByFullName,
                coaTaskEntity.createdTime, USERNAME, NAME, coaTaskEntity.reviewedTime);

        Assertions.assertEquals(taskOutputDto.taskId(), updatedTask.taskId());
        Assertions.assertEquals(taskOutputDto.spreadId(), updatedTask.spreadId());
        Assertions.assertEquals(taskOutputDto.blockId(), updatedTask.blockId());
        Assertions.assertEquals(taskOutputDto.rowId(), updatedTask.rowId());
        Assertions.assertEquals(taskOutputDto.tableTypeId(), updatedTask.tableTypeId());
        Assertions.assertEquals(taskOutputDto.coaId(), updatedTask.coaId());
        Assertions.assertEquals(taskOutputDto.status(), updatedTask.status());
        Assertions.assertEquals(taskOutputDto.createdBy(), updatedTask.createdBy());
        Assertions.assertEquals(taskOutputDto.createdByFullName(), updatedTask.createdByFullName());
        Assertions.assertEquals(taskOutputDto.reviewedBy(), updatedTask.reviewedBy());
        Assertions.assertEquals(taskOutputDto.reviewedByFullName(), updatedTask.reviewedByFullName());
    }

    @Test
    void testGet() {
        addCoaList();
        addNewTask(newTask);

        TaskOutputDto task = given().auth().oauth2(accessToken)
                .when().get("/task/1")
                .then().statusCode(200)
                .extract().body().as(TaskOutputDto.class);

        Assertions.assertEquals(1, task.taskId());
        Assertions.assertEquals(newTask.coaId(), task.coaId());
    }

    @Test
    void testList() {
        addCoaList();
        addNewTask(newTask);
        Mockito.when(iamClient.getNameFromUsername(USERNAME)).thenReturn(NAME);
        Mockito.when(iamClient.getNamesFromUsernames(List.of(USERNAME))).thenReturn(Map.of(USERNAME, NAME));

        CoaTaskDto.NewTask newTask2 = new CoaTaskDto.NewTask(2, 2, 2, 1, "BS",
                "rowParent2", "text2", "fsHeader2", "fsText2", 1);
        addNewTask(newTask2);
        CoaTaskDto.NewTask newTask3 = new CoaTaskDto.NewTask(3, 2, 3, 1, "BS",
                "rowParent3", "text3", "fsHeader3", "fsText3", 2);
        addNewTask(newTask3);
        CoaTaskDto.NewTask newTask4 = new CoaTaskDto.NewTask(4, 2, 2, 1, "IS",
                "rowParent4", "text4", "fsHeader4", "fsText4", 2);
        addNewTask(newTask4);
        CoaTaskDto.NewTask newTask5 = new CoaTaskDto.NewTask(5, 3, 3, 1, "NTA",
                "rowParent5", "text5", "fsHeader5", "fsText5", 3);
        addNewTask(newTask5);

        //Sort
        ListTaskDto.SortDto sort1 = new ListTaskDto.SortDto("spreadId", "DESC");
        ListTaskDto.SortDto sort2 = new ListTaskDto.SortDto("blockId", "DESC");
        List<ListTaskDto.SortDto> sortDtos = List.of(sort1, sort2);

        //spreadId Filter
        Integer spreadId = 2;
        ListTaskDto.FilterDto filter1 = new ListTaskDto.FilterDto("spreadId", spreadId.toString());

        List<StatusEnum> statusEnumList = List.of(StatusEnum.TO_REVIEW, StatusEnum.APPROVED);
        // Search "BS"
        ListTaskDto.GetTaskList taskListDto = new ListTaskDto.GetTaskList(1, 10, List.of(filter1),
                sortDtos, "BS", statusEnumList);

        TaskListResponseDto response = given().auth().oauth2(accessToken)
                .contentType(ContentType.JSON).body(taskListDto)
                .when().post("/task/list")
                .then().statusCode(200)
                .extract().body().as(TaskListResponseDto.class);
        List<TaskOutputDto> taskEntityList = response.tasks();

        Assertions.assertEquals(2, taskEntityList.size());

        TaskOutputDto item1 = taskEntityList.get(0);
        Assertions.assertEquals(spreadId, item1.spreadId());
        Assertions.assertEquals(newTask3.coaId(), item1.coaId());
        Assertions.assertEquals("BS", item1.tableTypeId());

        TaskOutputDto item2 = taskEntityList.get(1);
        Assertions.assertEquals(spreadId, item2.spreadId());
        Assertions.assertEquals(newTask2.coaId(), item2.coaId());
        Assertions.assertEquals("BS", item2.tableTypeId());

        // Test for different client name
        String clientToken = getAccessToken("Full Name", "Client Name2",
                Set.of("MAP_COA", "CREATE_NEW_COA", "ADMIN"));
        TaskListResponseDto response2 = given().auth().oauth2(clientToken)
                .contentType(ContentType.JSON).body(taskListDto)
                .when().post("/task/list")
                .then().statusCode(200)
                .extract().body().as(TaskListResponseDto.class);
        List<TaskOutputDto> taskEntityList2 = response2.tasks();

        Assertions.assertTrue(taskEntityList2.isEmpty());
    }*/
}
